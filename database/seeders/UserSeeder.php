<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 创建管理员用户
        User::create([
            'name' => '系统管理员',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
        ]);

        // 创建普通用户1
        User::create([
            'name' => '张三',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
        ]);

        // 创建普通用户2
        User::create([
            'name' => '李四',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('password123'),
        ]);
    }
}
