<?php

namespace Database\Seeders;

use App\Models\News;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

/**
 * 新闻资讯数据填充器
 */
class NewsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $newsData = [
            [
                'title' => '深入学习贯彻党的二十大精神，推动党建工作高质量发展',
                'summary' => '全面贯彻落实党的二十大精神，以高质量党建引领高质量发展，不断提升基层党组织的组织力和战斗力。',
                'content' => $this->getPartyNewsContent(),
                'type' => 'party_news',
                'category' => 'important',
                'author' => '党委宣传部',
                'source' => '本站原创',
                'images' => [
                    '/images/news/party-congress-1.jpg',
                    '/images/news/party-congress-2.jpg'
                ],
                'attachments' => [
                    [
                        'name' => '学习材料.pdf',
                        'url' => '/attachments/study-materials.pdf',
                        'size' => '2.5MB'
                    ]
                ],
                'external_url' => null,
                'is_featured' => true,
                'allow_comments' => true,
                'view_count' => rand(100, 500),
                'like_count' => rand(20, 100),
                'publish_date' => now()->subDays(2),
                'status' => 'published',
            ],
            [
                'title' => '关于印发《党员发展工作实施细则》的通知',
                'summary' => '为进一步规范党员发展工作，确保发展党员质量，根据《中国共产党发展党员工作细则》，制定本实施细则。',
                'content' => $this->getPolicyContent(),
                'type' => 'policy_regulation',
                'category' => 'important',
                'author' => '组织部',
                'source' => '组织部文件',
                'images' => null,
                'attachments' => [
                    [
                        'name' => '党员发展工作实施细则.pdf',
                        'url' => '/attachments/party-development-rules.pdf',
                        'size' => '1.8MB'
                    ]
                ],
                'external_url' => null,
                'is_featured' => true,
                'allow_comments' => false,
                'view_count' => rand(200, 800),
                'like_count' => rand(30, 150),
                'publish_date' => now()->subDays(5),
                'status' => 'published',
            ],
            [
                'title' => '我单位举办"学思想、强党性、重实践、建新功"主题教育活动',
                'summary' => '为深入开展学习贯彻习近平新时代中国特色社会主义思想主题教育，我单位组织开展了系列学习实践活动。',
                'content' => $this->getActivityContent(),
                'type' => 'activity_news',
                'category' => 'general',
                'author' => '张三',
                'source' => '第一党支部',
                'images' => [
                    '/images/news/activity-1.jpg',
                    '/images/news/activity-2.jpg',
                    '/images/news/activity-3.jpg'
                ],
                'attachments' => null,
                'external_url' => null,
                'is_featured' => false,
                'allow_comments' => true,
                'view_count' => rand(50, 200),
                'like_count' => rand(10, 50),
                'publish_date' => now()->subDays(7),
                'status' => 'published',
            ],
            [
                'title' => '优秀党员风采展示：李明同志先进事迹',
                'summary' => '李明同志在工作中始终以党员标准严格要求自己，在平凡的岗位上做出了不平凡的业绩。',
                'content' => $this->getPropagandaContent(),
                'type' => 'propaganda',
                'category' => 'general',
                'author' => '宣传部',
                'source' => '本站原创',
                'images' => [
                    '/images/news/excellent-member.jpg'
                ],
                'attachments' => null,
                'external_url' => null,
                'is_featured' => true,
                'allow_comments' => true,
                'view_count' => rand(80, 300),
                'like_count' => rand(15, 80),
                'publish_date' => now()->subDays(10),
                'status' => 'published',
            ],
            [
                'title' => '关于开展党员民主评议工作的通知',
                'summary' => '根据上级党委要求，决定在全体党员中开展2024年度民主评议党员工作。',
                'content' => $this->getAnnouncementContent(),
                'type' => 'party_news',
                'category' => 'announcement',
                'author' => '党委办公室',
                'source' => '党委文件',
                'images' => null,
                'attachments' => [
                    [
                        'name' => '民主评议工作方案.doc',
                        'url' => '/attachments/democratic-evaluation-plan.doc',
                        'size' => '856KB'
                    ]
                ],
                'external_url' => null,
                'is_featured' => false,
                'allow_comments' => false,
                'view_count' => rand(150, 400),
                'like_count' => rand(20, 60),
                'publish_date' => now()->subDays(3),
                'status' => 'published',
            ],
            [
                'title' => '中国共产党新闻网',
                'summary' => '中国共产党新闻网是由中共中央宣传部主管，人民网主办的党建门户网站。',
                'content' => '点击访问中国共产党新闻网，了解最新党建动态和政策解读。',
                'type' => 'external_link',
                'category' => 'general',
                'author' => '系统管理员',
                'source' => '外部链接',
                'images' => null,
                'attachments' => null,
                'external_url' => 'http://cpc.people.com.cn/',
                'is_featured' => false,
                'allow_comments' => false,
                'view_count' => rand(50, 150),
                'like_count' => rand(5, 30),
                'publish_date' => now()->subDays(1),
                'status' => 'published',
            ],
        ];

        foreach ($newsData as $data) {
            News::create($data);
            $this->command->info("创建新闻: {$data['title']}");
        }

        $this->command->info('新闻数据填充完成！');
    }

    /**
     * 获取党建要闻内容
     */
    private function getPartyNewsContent(): string
    {
        return <<<HTML
<h2>深入学习贯彻党的二十大精神</h2>

<p>党的二十大是在全党全国各族人民迈上全面建设社会主义现代化国家新征程、向第二个百年奋斗目标进军的关键时刻召开的一次十分重要的大会。</p>

<h3>主要内容</h3>
<ul>
<li>全面贯彻习近平新时代中国特色社会主义思想</li>
<li>坚持和加强党的全面领导</li>
<li>推进党的建设新的伟大工程</li>
<li>以高质量党建引领高质量发展</li>
</ul>

<h3>工作要求</h3>
<p>各级党组织要把学习贯彻党的二十大精神作为当前和今后一个时期的首要政治任务，切实把思想和行动统一到党的二十大精神上来。</p>

<blockquote>
<p>"全面建设社会主义现代化国家、全面推进中华民族伟大复兴，关键在党。"</p>
</blockquote>
HTML;
    }

    /**
     * 获取政策法规内容
     */
    private function getPolicyContent(): string
    {
        return <<<HTML
<h2>党员发展工作实施细则</h2>

<h3>第一章 总则</h3>
<p>第一条 为了规范发展党员工作，保证新发展的党员质量，保持党的先进性和纯洁性，根据《中国共产党章程》和《中国共产党发展党员工作细则》，结合实际，制定本实施细则。</p>

<h3>第二章 入党积极分子的确定和培养教育</h3>
<p>第二条 党组织应当通过宣传党的政治主张和深入细致的思想政治工作，提高党外群众对党的认识，不断扩大入党积极分子队伍。</p>

<h3>第三章 发展对象的确定和考察</h3>
<p>第三条 对经过一年以上培养教育和考察、基本具备党员条件的入党积极分子，在听取党小组、培养联系人、党员和群众意见的基础上，支部委员会讨论同意并报上级党委备案后，可列为发展对象。</p>
HTML;
    }

    /**
     * 获取活动新闻内容
     */
    private function getActivityContent(): string
    {
        return <<<HTML
<h2>主题教育活动圆满举办</h2>

<p>为深入开展学习贯彻习近平新时代中国特色社会主义思想主题教育，我单位于近日组织开展了"学思想、强党性、重实践、建新功"主题教育活动。</p>

<h3>活动亮点</h3>
<ul>
<li><strong>理论学习</strong>：组织专题学习会，深入学习党的创新理论</li>
<li><strong>实践活动</strong>：开展志愿服务，践行为民服务宗旨</li>
<li><strong>调查研究</strong>：深入基层了解实际情况，解决实际问题</li>
<li><strong>检视整改</strong>：查找不足，制定整改措施</li>
</ul>

<h3>活动成效</h3>
<p>通过此次主题教育活动，全体党员干部进一步增强了理论素养，提高了政治觉悟，强化了责任担当，为推动各项工作高质量发展奠定了坚实基础。</p>
HTML;
    }

    /**
     * 获取宣传报道内容
     */
    private function getPropagandaContent(): string
    {
        return <<<HTML
<h2>优秀党员李明同志先进事迹</h2>

<p>李明同志自入党以来，始终以一名优秀共产党员的标准严格要求自己，在平凡的工作岗位上默默奉献，用实际行动诠释了共产党员的初心和使命。</p>

<h3>主要事迹</h3>
<ul>
<li>工作兢兢业业，连续三年被评为先进工作者</li>
<li>积极参与志愿服务，累计服务时长超过200小时</li>
<li>主动承担急难险重任务，展现党员担当</li>
<li>热心帮助同事，营造和谐工作氛围</li>
</ul>

<h3>获得荣誉</h3>
<p>李明同志先后获得"优秀党员"、"先进工作者"、"优秀志愿者"等荣誉称号，是全体党员学习的榜样。</p>
HTML;
    }

    /**
     * 获取公告内容
     */
    private function getAnnouncementContent(): string
    {
        return <<<HTML
<h2>关于开展党员民主评议工作的通知</h2>

<h3>评议时间</h3>
<p>2024年12月1日至12月31日</p>

<h3>评议对象</h3>
<p>全体正式党员和预备党员</p>

<h3>评议内容</h3>
<ul>
<li>理想信念坚定情况</li>
<li>政治立场和政治纪律遵守情况</li>
<li>宗旨意识和作风建设情况</li>
<li>履职尽责和发挥作用情况</li>
<li>道德品行和廉洁自律情况</li>
</ul>

<h3>工作要求</h3>
<p>各党支部要高度重视，精心组织，确保民主评议工作取得实效。要坚持实事求是，客观公正，严肃认真地开展评议工作。</p>
HTML;
    }
}
