<?php

namespace Database\Seeders;

use App\Models\Exam;
use App\Models\Training;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

/**
 * 考试数据填充器
 */
class ExamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 获取培训数据
        $trainings = Training::all();

        // 创建考试数据
        $exams = [
            [
                'title' => '党的二十大精神学习考试',
                'description' => '测试党员对党的二十大精神的理解和掌握程度',
                'training_id' => $trainings->isNotEmpty() ? $trainings->first()->id : null,
                'type' => 'training_exam',
                'questions' => [
                    [
                        'id' => 1,
                        'type' => 'single_choice',
                        'question' => '党的二十大报告指出，中国式现代化的本质要求是什么？',
                        'options' => [
                            'A' => '坚持中国共产党领导',
                            'B' => '坚持中国特色社会主义',
                            'C' => '实现高质量发展',
                            'D' => '以上都是'
                        ],
                        'correct_answer' => 'D',
                        'score' => 10
                    ],
                    [
                        'id' => 2,
                        'type' => 'single_choice',
                        'question' => '全面建设社会主义现代化国家的首要任务是什么？',
                        'options' => [
                            'A' => '高质量发展',
                            'B' => '共同富裕',
                            'C' => '科技创新',
                            'D' => '文化建设'
                        ],
                        'correct_answer' => 'A',
                        'score' => 10
                    ],
                    [
                        'id' => 3,
                        'type' => 'multiple_choice',
                        'question' => '新时代十年的伟大变革包括哪些方面？（多选）',
                        'options' => [
                            'A' => '经济实力实现历史性跃升',
                            'B' => '改革开放迈出重大步伐',
                            'C' => '全面从严治党取得重大成果',
                            'D' => '国际影响力显著提升'
                        ],
                        'correct_answer' => ['A', 'B', 'C', 'D'],
                        'score' => 15
                    ],
                    [
                        'id' => 4,
                        'type' => 'true_false',
                        'question' => '中国式现代化是人口规模巨大的现代化。',
                        'correct_answer' => true,
                        'score' => 5
                    ],
                    [
                        'id' => 5,
                        'type' => 'essay',
                        'question' => '请简述你对"以中国式现代化全面推进中华民族伟大复兴"的理解。',
                        'score' => 20,
                        'answer_requirements' => '不少于200字，结合实际工作谈理解'
                    ]
                ],
                'duration_minutes' => 60,
                'pass_score' => 70.00,
                'max_attempts' => 2,
                'randomize_questions' => true,
                'show_results_immediately' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(3),
                'instructions' => '请认真阅读题目，仔细作答。考试时间为60分钟，合格分数为70分。',
                'status' => 'published',
            ],
            [
                'title' => '党风廉政建设知识测试',
                'description' => '检验党员干部对党风廉政建设相关知识的掌握情况',
                'training_id' => null,
                'type' => 'qualification_exam',
                'questions' => [
                    [
                        'id' => 1,
                        'type' => 'single_choice',
                        'question' => '《中国共产党纪律处分条例》规定的纪律处分种类有几种？',
                        'options' => [
                            'A' => '4种',
                            'B' => '5种',
                            'C' => '6种',
                            'D' => '7种'
                        ],
                        'correct_answer' => 'B',
                        'score' => 10
                    ],
                    [
                        'id' => 2,
                        'type' => 'single_choice',
                        'question' => '党员受到开除党籍处分，几年内不得重新入党？',
                        'options' => [
                            'A' => '3年',
                            'B' => '4年',
                            'C' => '5年',
                            'D' => '终身'
                        ],
                        'correct_answer' => 'C',
                        'score' => 10
                    ],
                    [
                        'id' => 3,
                        'type' => 'true_false',
                        'question' => '党员干部可以在私人会所举办婚丧喜庆事宜。',
                        'correct_answer' => false,
                        'score' => 10
                    ]
                ],
                'duration_minutes' => 45,
                'pass_score' => 80.00,
                'max_attempts' => 3,
                'randomize_questions' => false,
                'show_results_immediately' => false,
                'available_from' => now()->subDays(7),
                'available_until' => now()->addMonths(1),
                'instructions' => '本次考试为党风廉政建设知识测试，请严格按照要求作答。',
                'status' => 'published',
            ],
            [
                'title' => '基层党建工作实务考核',
                'description' => '考核基层党务工作者的实际工作能力和业务水平',
                'training_id' => $trainings->count() > 1 ? $trainings->skip(1)->first()->id : null,
                'type' => 'assessment',
                'questions' => [
                    [
                        'id' => 1,
                        'type' => 'essay',
                        'question' => '如何组织开展一次高质量的支部党员大会？请从会前准备、会议流程、会后总结等方面详细说明。',
                        'score' => 25,
                        'answer_requirements' => '不少于300字，结合实际工作经验'
                    ],
                    [
                        'id' => 2,
                        'type' => 'essay',
                        'question' => '在发展党员工作中，如何确保发展质量？请谈谈你的做法和体会。',
                        'score' => 25,
                        'answer_requirements' => '不少于250字，要有具体措施'
                    ]
                ],
                'duration_minutes' => 90,
                'pass_score' => 75.00,
                'max_attempts' => 1,
                'randomize_questions' => false,
                'show_results_immediately' => false,
                'available_from' => now()->addDays(7),
                'available_until' => now()->addMonths(2),
                'instructions' => '本次考核为实务操作考核，请结合实际工作经验作答，注意答题的针对性和实用性。',
                'status' => 'draft',
            ],
        ];

        foreach ($exams as $examData) {
            Exam::create($examData);
            $this->command->info("创建考试: {$examData['title']}");
        }

        $this->command->info('考试数据填充完成！');
    }
}
