<?php

namespace Database\Seeders;

use App\Models\PartyMember;
use App\Models\User;
use App\Models\PartyOrganization;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

/**
 * 党员数据填充器
 */
class PartyMemberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 获取用户和党组织
        $users = User::all();
        $partyOrganizations = PartyOrganization::all();

        if ($users->isEmpty() || $partyOrganizations->isEmpty()) {
            $this->command->warn('请先运行 UserSeeder 和 PartyOrganizationSeeder');
            return;
        }

        // 创建党员数据
        $partyMembers = [
            [
                'user_id' => $users->first()->id, // 管理员用户
                'party_organization_id' => $partyOrganizations->first()->id,
                'party_number' => 'DY202401001',
                'member_type' => 'formal',
                'join_date' => now()->subYears(5),
                'formal_date' => now()->subYears(4),
                'introduction_person' => '王书记',
                'contact_person' => '李主任',
                'awards' => [
                    [
                        'title' => '优秀党员',
                        'date' => '2023-07-01',
                        'level' => '支部级',
                        'description' => '在党建工作中表现突出'
                    ]
                ],
                'punishments' => [],
                'transfer_history' => [],
                'remarks' => '党建工作积极分子，具有丰富的组织管理经验',
                'status' => 'active',
            ],
            [
                'user_id' => $users->skip(1)->first()->id, // 张三
                'party_organization_id' => $partyOrganizations->first()->id,
                'party_number' => 'DY202401002',
                'member_type' => 'formal',
                'join_date' => now()->subYears(3),
                'formal_date' => now()->subYears(2),
                'introduction_person' => '系统管理员',
                'contact_person' => '王书记',
                'awards' => [],
                'punishments' => [],
                'transfer_history' => [],
                'remarks' => '工作认真负责，积极参与党组织活动',
                'status' => 'active',
            ],
            [
                'user_id' => $users->skip(2)->first()->id, // 李四
                'party_organization_id' => $partyOrganizations->count() > 1 ? $partyOrganizations->skip(1)->first()->id : $partyOrganizations->first()->id,
                'party_number' => 'DY202401003',
                'member_type' => 'probationary',
                'join_date' => now()->subMonths(6),
                'formal_date' => null,
                'introduction_person' => '张三',
                'contact_person' => '系统管理员',
                'awards' => [],
                'punishments' => [],
                'transfer_history' => [],
                'remarks' => '预备党员，表现良好，积极向党组织靠拢',
                'status' => 'active',
            ],
        ];

        foreach ($partyMembers as $memberData) {
            // 检查用户是否已经是党员
            $existingMember = PartyMember::where('user_id', $memberData['user_id'])->first();
            if (!$existingMember) {
                PartyMember::create($memberData);
                $this->command->info("创建党员: {$memberData['party_number']}");
            }
        }

        // 如果有更多用户，创建更多党员
        $remainingUsers = $users->skip(3);
        $memberTypes = ['applicant', 'activist', 'probationary', 'formal'];
        
        foreach ($remainingUsers as $index => $user) {
            $existingMember = PartyMember::where('user_id', $user->id)->first();
            if (!$existingMember) {
                $memberType = $memberTypes[$index % count($memberTypes)];
                $joinDate = now()->subMonths(rand(1, 36));
                
                PartyMember::create([
                    'user_id' => $user->id,
                    'party_organization_id' => $partyOrganizations->random()->id,
                    'party_number' => 'DY' . date('Y') . str_pad($index + 4, 3, '0', STR_PAD_LEFT),
                    'member_type' => $memberType,
                    'join_date' => $joinDate,
                    'formal_date' => $memberType === 'formal' ? $joinDate->addYear() : null,
                    'introduction_person' => $users->random()->name,
                    'contact_person' => $users->random()->name,
                    'awards' => [],
                    'punishments' => [],
                    'transfer_history' => [],
                    'remarks' => "系统生成的{$memberType}党员数据",
                    'status' => 'active',
                ]);
            }
        }

        $this->command->info('党员数据填充完成！');
    }
}
