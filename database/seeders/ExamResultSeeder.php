<?php

namespace Database\Seeders;

use App\Models\ExamResult;
use App\Models\Exam;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

/**
 * 考试结果数据填充器
 */
class ExamResultSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 获取考试和用户数据
        $exams = Exam::where('status', 'published')->get();
        $users = User::all();

        if ($exams->isEmpty() || $users->isEmpty()) {
            $this->command->warn('请先运行 ExamSeeder 和 UserSeeder');
            return;
        }

        // 为每个用户创建考试结果
        foreach ($users as $user) {
            foreach ($exams as $exam) {
                // 随机决定是否参加考试（80%概率）
                if (rand(1, 100) <= 80) {
                    $this->createExamResult($exam, $user);
                }
            }
        }

        $this->command->info('考试结果数据填充完成！');
    }

    /**
     * 创建考试结果
     */
    private function createExamResult(Exam $exam, User $user): void
    {
        $questions = $exam->questions;
        $answers = [];
        $questionResults = [];
        $totalScore = 0;
        $maxScore = 0;

        // 模拟答题
        foreach ($questions as $question) {
            $maxScore += $question['score'];
            $isCorrect = rand(1, 100) <= 75; // 75%正确率

            switch ($question['type']) {
                case 'single_choice':
                    $userAnswer = $isCorrect ? $question['correct_answer'] : $this->getRandomOption($question['options']);
                    $answers[$question['id']] = $userAnswer;
                    $score = ($userAnswer === $question['correct_answer']) ? $question['score'] : 0;
                    break;

                case 'multiple_choice':
                    if ($isCorrect) {
                        $userAnswer = $question['correct_answer'];
                    } else {
                        // 随机选择部分正确答案或错误答案
                        $userAnswer = $this->getRandomMultipleChoice($question['options'], $question['correct_answer']);
                    }
                    $answers[$question['id']] = $userAnswer;
                    $score = (array_diff($userAnswer, $question['correct_answer']) === [] && 
                             array_diff($question['correct_answer'], $userAnswer) === []) ? $question['score'] : 0;
                    break;

                case 'true_false':
                    $userAnswer = $isCorrect ? $question['correct_answer'] : !$question['correct_answer'];
                    $answers[$question['id']] = $userAnswer;
                    $score = ($userAnswer === $question['correct_answer']) ? $question['score'] : 0;
                    break;

                case 'essay':
                    $userAnswer = $this->generateEssayAnswer($question['question']);
                    $answers[$question['id']] = $userAnswer;
                    // 主观题随机给分（60%-100%）
                    $score = $question['score'] * (rand(60, 100) / 100);
                    break;

                default:
                    $userAnswer = '';
                    $score = 0;
            }

            $totalScore += $score;
            $questionResults[] = [
                'question_id' => $question['id'],
                'user_answer' => $userAnswer,
                'correct_answer' => $question['correct_answer'] ?? null,
                'score' => $score,
                'max_score' => $question['score'],
                'is_correct' => $score > 0
            ];
        }

        $finalScore = round($totalScore, 2);
        $passed = $finalScore >= $exam->pass_score;

        // 随机生成考试时间
        $startTime = now()->subDays(rand(1, 30))->addHours(rand(8, 18));
        $timeSpent = rand(15, $exam->duration_minutes);
        $endTime = $startTime->copy()->addMinutes($timeSpent);

        ExamResult::create([
            'exam_id' => $exam->id,
            'user_id' => $user->id,
            'attempt_number' => 1,
            'answers' => $answers,
            'score' => $finalScore,
            'passed' => $passed,
            'time_spent_minutes' => $timeSpent,
            'started_at' => $startTime,
            'completed_at' => $endTime,
            'question_results' => $questionResults,
            'feedback' => $this->generateFeedback($passed, $finalScore, $exam->pass_score),
        ]);

        $this->command->info("创建考试结果: {$user->name} - {$exam->title} - 分数: {$finalScore}");
    }

    /**
     * 获取随机选项
     */
    private function getRandomOption(array $options): string
    {
        $keys = array_keys($options);
        return $keys[array_rand($keys)];
    }

    /**
     * 获取随机多选答案
     */
    private function getRandomMultipleChoice(array $options, array $correctAnswers): array
    {
        $keys = array_keys($options);
        $numSelections = rand(1, count($keys));
        $selected = array_rand($keys, $numSelections);
        
        if (!is_array($selected)) {
            $selected = [$selected];
        }
        
        return array_map(fn($index) => $keys[$index], $selected);
    }

    /**
     * 生成主观题答案
     */
    private function generateEssayAnswer(string $question): string
    {
        $templates = [
            "根据题目要求，我认为{$question}的核心在于...",
            "从实际工作经验来看，{$question}需要从以下几个方面考虑...",
            "结合党的理论学习和实践工作，我对{$question}的理解是...",
        ];
        
        return $templates[array_rand($templates)] . "（此为系统生成的示例答案）";
    }

    /**
     * 生成考试反馈
     */
    private function generateFeedback(bool $passed, float $score, float $passScore): string
    {
        if ($passed) {
            return "恭喜您通过了考试！您的得分为 {$score} 分，超过了合格线 {$passScore} 分。请继续保持学习的热情。";
        } else {
            return "很遗憾，您本次考试未能通过。您的得分为 {$score} 分，距离合格线 {$passScore} 分还有一定差距。建议您加强相关知识的学习后再次参加考试。";
        }
    }
}
