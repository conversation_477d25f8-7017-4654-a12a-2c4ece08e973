<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('three_one_meetings', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('会议标题');
            $table->enum('type', ['branch_meeting', 'committee_meeting', 'group_meeting', 'party_class'])
                  ->comment('会议类型：支部党员大会/支委会/党小组会/党课');
            $table->foreignId('party_organization_id')->constrained()->comment('党组织ID');
            $table->text('content')->comment('会议内容');
            $table->text('agenda')->nullable()->comment('会议议程');
            $table->string('venue')->comment('会议地点');
            $table->datetime('meeting_date')->comment('会议日期');
            $table->integer('duration_minutes')->default(120)->comment('会议时长(分钟)');
            $table->string('host')->comment('主持人');
            $table->string('recorder')->nullable()->comment('记录人');
            $table->json('participants')->nullable()->comment('参会人员');
            $table->json('absent_members')->nullable()->comment('缺席人员');
            $table->text('summary')->nullable()->comment('会议总结');
            $table->json('resolutions')->nullable()->comment('会议决议');
            $table->json('attachments')->nullable()->comment('附件');
            $table->enum('status', ['planned', 'ongoing', 'completed', 'cancelled'])
                  ->default('planned')->comment('状态');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['party_organization_id', 'type']);
            $table->index(['meeting_date', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('three_one_meetings');
    }
};