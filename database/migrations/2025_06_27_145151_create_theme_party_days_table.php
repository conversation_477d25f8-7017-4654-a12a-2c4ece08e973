<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('theme_party_days', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('活动标题');
            $table->string('theme')->comment('活动主题');
            $table->foreignId('party_organization_id')->constrained()->comment('党组织ID');
            $table->text('description')->comment('活动描述');
            $table->text('objectives')->nullable()->comment('活动目标');
            $table->string('venue')->comment('活动地点');
            $table->datetime('activity_date')->comment('活动日期');
            $table->integer('duration_hours')->default(4)->comment('活动时长(小时)');
            $table->string('organizer')->comment('组织者');
            $table->json('participants')->nullable()->comment('参与人员');
            $table->json('activities')->nullable()->comment('活动安排');
            $table->json('materials')->nullable()->comment('活动材料');
            $table->text('summary')->nullable()->comment('活动总结');
            $table->json('photos')->nullable()->comment('活动照片');
            $table->json('feedback')->nullable()->comment('参与反馈');
            $table->decimal('budget', 10, 2)->nullable()->comment('活动预算');
            $table->decimal('actual_cost', 10, 2)->nullable()->comment('实际费用');
            $table->enum('status', ['planned', 'ongoing', 'completed', 'cancelled'])
                  ->default('planned')->comment('状态');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['party_organization_id', 'theme']);
            $table->index(['activity_date', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('theme_party_days');
    }
};