<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 考试结果模型
 */
class ExamResult extends Model
{
    protected $fillable = [
        'exam_id',
        'user_id',
        'attempt_number',
        'answers',
        'score',
        'passed',
        'time_spent_minutes',
        'started_at',
        'completed_at',
        'question_results',
        'feedback',
    ];

    protected $casts = [
        'answers' => 'array',
        'score' => 'decimal:2',
        'passed' => 'boolean',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'question_results' => 'array',
    ];

    /**
     * 关联考试
     */
    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
