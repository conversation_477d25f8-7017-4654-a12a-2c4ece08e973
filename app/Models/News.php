<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * 新闻资讯模型
 */
class News extends Model
{
    protected $fillable = [
        'title',
        'summary',
        'content',
        'type',
        'category',
        'author',
        'source',
        'images',
        'attachments',
        'external_url',
        'is_featured',
        'allow_comments',
        'view_count',
        'like_count',
        'publish_date',
        'status',
    ];

    protected $casts = [
        'images' => 'array',
        'attachments' => 'array',
        'is_featured' => 'boolean',
        'allow_comments' => 'boolean',
        'publish_date' => 'date',
    ];

    /**
     * 类型常量
     */
    const TYPE_PARTY_NEWS = 'party_news';
    const TYPE_POLICY_REGULATION = 'policy_regulation';
    const TYPE_PROPAGANDA = 'propaganda';
    const TYPE_ACTIVITY_NEWS = 'activity_news';
    const TYPE_EXTERNAL_LINK = 'external_link';

    /**
     * 分类常量
     */
    const CATEGORY_IMPORTANT = 'important';
    const CATEGORY_GENERAL = 'general';
    const CATEGORY_ANNOUNCEMENT = 'announcement';

    /**
     * 状态常量
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_PUBLISHED = 'published';
    const STATUS_ARCHIVED = 'archived';

    /**
     * 获取类型中文名称
     */
    public function getTypeNameAttribute(): string
    {
        return match($this->type) {
            self::TYPE_PARTY_NEWS => '党建要闻',
            self::TYPE_POLICY_REGULATION => '政策法规',
            self::TYPE_PROPAGANDA => '宣传报道',
            self::TYPE_ACTIVITY_NEWS => '活动动态',
            self::TYPE_EXTERNAL_LINK => '外部链接',
            default => '未知类型',
        };
    }

    /**
     * 获取分类中文名称
     */
    public function getCategoryNameAttribute(): string
    {
        return match($this->category) {
            self::CATEGORY_IMPORTANT => '重要',
            self::CATEGORY_GENERAL => '一般',
            self::CATEGORY_ANNOUNCEMENT => '公告',
            default => '未知分类',
        };
    }

    /**
     * 获取状态中文名称
     */
    public function getStatusNameAttribute(): string
    {
        return match($this->status) {
            self::STATUS_DRAFT => '草稿',
            self::STATUS_PUBLISHED => '已发布',
            self::STATUS_ARCHIVED => '已归档',
            default => '未知状态',
        };
    }
}
