<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 考试模型
 */
class Exam extends Model
{
    protected $fillable = [
        'title',
        'description',
        'training_id',
        'type',
        'questions',
        'duration_minutes',
        'pass_score',
        'max_attempts',
        'randomize_questions',
        'show_results_immediately',
        'available_from',
        'available_until',
        'instructions',
        'status',
    ];

    protected $casts = [
        'questions' => 'array',
        'pass_score' => 'decimal:2',
        'randomize_questions' => 'boolean',
        'show_results_immediately' => 'boolean',
        'available_from' => 'date',
        'available_until' => 'date',
    ];

    /**
     * 关联培训
     */
    public function training(): BelongsTo
    {
        return $this->belongsTo(Training::class);
    }

    /**
     * 关联考试结果
     */
    public function results(): HasMany
    {
        return $this->hasMany(ExamResult::class);
    }
}
